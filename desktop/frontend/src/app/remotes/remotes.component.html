<!-- Remotes List -->
<div class="p-6 max-w-4xl mx-auto">
  <div
    *ngIf="(appService.remotes$ | async)?.length; else noRemotes"
    class="card"
  >
    <div class="mb-6">
      <div class="flex items-center space-x-3 mb-2">
        <lucide-icon
          [img]="CloudIcon"
          class="w-6 h-6 text-primary-600"
        ></lucide-icon>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Connected Remotes
        </h1>
      </div>
      <p class="text-gray-600 dark:text-gray-400">
        {{ (appService.remotes$ | async)?.length }} remote(s) configured
      </p>
    </div>

    <div class="space-y-3">
      <div
        *ngFor="let remote of appService.remotes$ | async"
        class="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 group"
      >
        <div class="flex-shrink-0 mr-4">
          <svg
            class="w-8 h-8 text-gray-600 dark:text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
            ></path>
          </svg>
        </div>
        <div class="flex-1 min-w-0">
          <h3
            class="text-lg font-medium text-gray-900 dark:text-gray-100 truncate"
          >
            {{ remote.name }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ getRemoteTypeLabel(remote.type) }}
          </p>
        </div>
        <div class="flex-shrink-0 ml-4">
          <button
            (click)="confirmDeleteRemote(remote)"
            class="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-100 dark:hover:bg-red-900 rounded-lg transition-colors duration-200 opacity-0 group-hover:opacity-100"
            title="Delete remote"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              ></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- No Remotes State -->
<ng-template #noRemotes>
  <div class="p-6 max-w-4xl mx-auto">
    <div class="card text-center">
      <div class="mb-6">
        <div class="flex justify-center mb-4">
          <div
            class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"
          >
            <svg
              class="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
              ></path>
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </div>
        </div>
        <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          No Remotes Configured
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          Add your first cloud storage connection
        </p>
      </div>
      <div>
        <p class="text-gray-700 dark:text-gray-300 mb-6">
          Remotes allow you to sync files with cloud storage services like
          Google Drive, Dropbox, OneDrive, and more.
        </p>
        <button class="btn-primary" (click)="openAddRemoteDialog()">
          <svg
            class="w-5 h-5 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            ></path>
          </svg>
          Add First Remote
        </button>
      </div>
    </div>
  </div>
</ng-template>

<!-- Floating Action Button -->
<button
  (click)="openAddRemoteDialog()"
  class="fixed bottom-6 right-6 w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50"
  title="Add Remote"
>
  <lucide-icon [img]="PlusIcon" class="w-6 h-6"></lucide-icon>
</button>

<!-- Add Remote Modal -->
<div
  *ngIf="showAddRemoteModal"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
  (click)="closeAddRemoteModal()"
>
  <div
    class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-md mx-4"
    (click)="$event.stopPropagation()"
  >
    <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">
      Add New Remote
    </h2>

    <form (ngSubmit)="saveRemote()" class="space-y-4">
      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          Remote Name
        </label>
        <input
          type="text"
          class="input-field"
          [(ngModel)]="addRemoteData.name"
          name="name"
          placeholder="Enter remote name"
          required
        />
      </div>

      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
        >
          Remote Type
        </label>
        <select
          class="select-field"
          [(ngModel)]="addRemoteData.type"
          name="type"
          required
        >
          <option value="drive">Google Drive</option>
          <option value="dropbox">Dropbox</option>
          <option value="onedrive">OneDrive</option>
          <option value="yandex">Yandex Disk</option>
          <option value="gphotos">Google Photos</option>
        </select>
      </div>

      <div class="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          class="btn-secondary"
          (click)="closeAddRemoteModal()"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="btn-primary"
          [disabled]="
            !addRemoteData.name ||
            !addRemoteData.type ||
            (isAddingRemote$ | async)
          "
        >
          <span *ngIf="!(isAddingRemote$ | async)">Add Remote</span>
          <span *ngIf="isAddingRemote$ | async" class="flex items-center">
            <div
              class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
            ></div>
            Adding...
          </span>
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div
  *ngIf="showDeleteConfirmModal"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
  (click)="closeDeleteConfirmModal()"
>
  <div
    class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-md mx-4"
    (click)="$event.stopPropagation()"
  >
    <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">
      Confirm Delete
    </h2>

    <div class="mb-6">
      <p class="text-gray-700 dark:text-gray-300">
        Are you sure you want to delete remote
        <strong class="text-gray-900 dark:text-gray-100"
          >"{{ remoteToDelete?.name }}"</strong
        >?
      </p>
      <p class="text-red-600 dark:text-red-400 text-sm mt-2">
        This action cannot be undone.
      </p>
    </div>

    <div class="flex justify-end space-x-3">
      <button class="btn-secondary" (click)="closeDeleteConfirmModal()">
        Cancel
      </button>
      <button
        class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
        (click)="deleteRemote()"
      >
        Delete
      </button>
    </div>
  </div>
</div>
